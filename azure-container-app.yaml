# Azure Container Apps configuration
# Deploy this using Azure CLI or Azure Portal

apiVersion: apps/v1
kind: Deployment
metadata:
  name: verdaccio-deployment
spec:
  replicas: 1
  template:
    spec:
      containers:
      - name: verdaccio
        image: your-acr.azurecr.io/verdaccio-standalone-server-verdaccio:latest
        ports:
        - containerPort: 4873
        env:
        - name: VERDACCIO_PUBLIC_URL
          value: "https://your-app.azurecontainerapps.io/packages"
        
      - name: nginx-proxy
        image: nginx:alpine
        ports:
        - containerPort: 80
        # Mount nginx.conf as configmap
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
      
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
