storage: /verdaccio/storage
auth:
  htpasswd:
    file: /verdaccio/storage/htpasswd
    max_users: 100

uplinks:
  npmjs:
    url: https://registry.npmjs.org/

packages:
  '@ava/*':
    access: $all
    publish: $authenticated
    proxy: false

  '**':
    access: $all
    publish: $authenticated
    proxy: npmjs

middlewares:
  audit:
    enabled: true

# Configure to listen on port 80 for Azure App Service
listen: 0.0.0.0:80

max_body_size: 200mb
